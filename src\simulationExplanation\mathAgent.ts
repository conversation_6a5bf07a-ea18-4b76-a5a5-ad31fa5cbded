import { Agent, AgentInputItem, run, setDefaultOpenAIKey, setTracingExportApiKey, tool } from '@openai/agents';
import z from 'zod';
import { evaluate } from 'mathjs';
import dotenv from 'dotenv';
import { FullData } from './types';
import fs from 'fs';

dotenv.config();

setDefaultOpenAIKey(process.env.OPENAI_API_KEY);
setTracingExportApiKey(process.env.OPENAI_API_KEY);

const calculator = tool({
    name: 'Calculator',
    description: 'A calculator that can evaluate math expressions',
    parameters: z.object({
        expression: z.string().describe('The math expression to evaluate'),
    }),
    execute: async (expression) => {
        return evaluate(expression);
    } 
})

export async function generateMathExplanation(questionData: FullData, imagePath?: string) {
    const verbosities = {
        Easy: "low",
        Medium: "medium",
        Hard: "high"
    }

    const agent = new Agent({
        name: "Math Agent",
        instructions:
            "You are a high school math teacher teaching students how to solve SAT math problems. Your task is to provide a step-by-step explanation of how to solve a problem. Use latex to format your answer. Use $$...$$ for display equations and $...$ for inline equations. If the expression contains a dollar sign, escape it with a backslash. For example, $500 becomes $\\$500.$. Return your answer in the following JSON format: {\"correctAnswer\": \"<the INDEX of the correct answer (so 0 for A, 1 for B, etc.)>\", \"explanation\": \"<explanation>\"}",
        tools: [calculator],
        model: "gpt-5",
        outputType: z.object({
            correctAnswer: z.string().describe("The correct answer to the question"),
            explanation: z
            .string()
            .describe("A step-by-step explanation of how to solve the problem"),
        }),
        modelSettings: {
            providerData: {
                reasoning: { effort: questionData.difficulty === 'Hard' ? "medium" : "low" },
                text: { verbosity: verbosities[questionData.difficulty] }
            }
        }
    });

    if (questionData.difficulty === 'Hard') {
    
    }



    const inputItem: AgentInputItem = {
        role: 'user',
        content: []
    }


    if (imagePath) {
        // @ts-expect-error
        inputItem.content.push({
            type: 'input_image',
            image: "data:image/png;base64," + fs.readFileSync(imagePath, { encoding: "base64" }),
        });
    }

    // @ts-expect-error
    inputItem.content.push({
        type: 'input_text',
        text: `
        ${questionData.intro ?? ""}\n${questionData.passage ?? ""}\n${questionData.question ?? ""}\n${questionData.answer_choices ? questionData.answer_choices.map((choice, index) => `${String.fromCharCode(65 + index)}. ${choice}`).join('\n') : ""}`
    });

    const result = run(
        agent,
        [ inputItem ]
    ).catch(error => {
        console.error('Error running agent:', error);
    });

    return result;
}