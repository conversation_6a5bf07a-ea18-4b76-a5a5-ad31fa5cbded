import PDFDocument from 'pdfkit';
import fs from 'fs';
import boundariesQuestions from '../../static/boundaries-questions.json';
import grammarQuestions from '../../static/grammar-questions.json';
import transitionsQuestions from '../../static/transitions-questions.json';
import topicsJSON from '../../static/topics.json';
import { createQuestion, WritingQuestionType } from './workflow.ts';
import { QuestionData, TopicEntry } from '../types.ts';
import { QuestionResult } from '../types.ts';
import { supabase } from '../supabase.ts';

interface SSQBQuestion {
    passage: string;
    answerChoices: string[];
    correctAnswer: string;
    difficulty: string;
    rationale: string;
    isUsed: boolean;
}


main()

async function main() {
    // Convert JSON data to our interface format
    function convertJsonToQuestionData(jsonData: SSQBQuestion): QuestionData {
        return {
            originalPassage: jsonData.passage,
            originalChoices: jsonData.answerChoices,
            originalCorrectAnswerContent: jsonData.correctAnswer,
            difficulty: jsonData.difficulty,
            rationale: jsonData.rationale
        };
    }

    // Create new questions based on the old ones
    const questionType: WritingQuestionType = 'Grammar';

    const questionTypeToFile: Record<WritingQuestionType, SSQBQuestion[]> = {
        'Transitions': transitionsQuestions,
        'Punctuation': boundariesQuestions,
        'Grammar': grammarQuestions,
    }

    const questionsFile = questionTypeToFile[questionType];

    // Get 10 random questions from the questions
    const randomIndices = Array.from({length: questionsFile.length}, (_, i) => i)
        .sort(() => Math.random() - 0.5)

    const questions: QuestionData[] = randomIndices.map(index =>
        convertJsonToQuestionData(questionsFile[index])
    );

    const usedTopics = new Set<string>();

    const allQuestions = await Promise.all(questions.map((questionData) => {
        // Select a random topic and topic instance
        const topics: ("Social Science" | "Natural Science" | "Humanities")[] = ['Social Science', 'Natural Science', 'Humanities'];
        const topic = topics[Math.floor(Math.random() * topics.length)];
        const unusedTopics = (topicsJSON[topic] as TopicEntry[]).filter(t => !t.hasTopicBeenUsed && !usedTopics.has(t.topic));
        const topicEntry: TopicEntry = unusedTopics[Math.floor(Math.random() * unusedTopics.length)];
        usedTopics.add(topicEntry.topic);
        topicsJSON[topic].find(t => t.topic === topicEntry.topic)!.hasTopicBeenUsed = true;

        return createQuestion(questionData, questionType, topic, topicEntry);
    }));

    const validQuestions = allQuestions.filter(q => q);
    const validOriginalIndices: number[] = allQuestions
        .map((q, idx) => (q ? randomIndices[idx] : null))
        .filter((i): i is number => i !== null);


    if (validQuestions.length === 0) {
        console.error('No valid questions were generated');
        return;
    }

    console.log(`Generated ${validQuestions.length} valid questions.`);

    // Upload to supabase
    const { error } = await supabase
        .from('question')
        .insert(validQuestions);

    if (error) {
        console.error('Error storing questions:', error);
        return;
    }    

    // Mark topic and question as used
    fs.writeFileSync('./static/topics.json', JSON.stringify(topicsJSON, null, 2));

}




export function generateQuestionsPDF(questions: QuestionResult[], outputPath: string = 'generated_questions.pdf', originalIndices?: number[]): void {
    const doc = new PDFDocument({
        margin: 50,
        size: 'A4'
    });

    // Create a write stream
    doc.pipe(fs.createWriteStream(outputPath));

    // Add title
    doc.fontSize(20).font('Helvetica-Bold').text('SAT Practice Questions', { align: 'center' });
    doc.moveDown(2);

    questions.forEach((question, index) => {
        // Add page break for questions after the first
        if (index > 0) {
            doc.addPage();
        }

        // Question number and difficulty
        const difficultyMap = { 'E': 'Easy', 'M': 'Medium', 'H': 'Hard' };
        const difficultyText = difficultyMap[question.difficulty as keyof typeof difficultyMap] || question.difficulty;

        doc.fontSize(16).font('Helvetica-Bold').text(`Question ${index + 1}`, { underline: true, continued: true });
        doc.fontSize(12).font('Helvetica').fillColor('blue').text(` (${difficultyText})`, { underline: false });
        doc.fillColor('black');
        if (originalIndices && originalIndices[index] !== undefined) {
            doc.fontSize(10).font('Helvetica').fillColor('gray').text(`Original index: ${originalIndices[index]}`);
            doc.fillColor('black');
        }
        doc.moveDown(1);

        // Format passage with proper line breaks and indentation
        const formattedPassage = question.passage.replace(/_+/g, '________');
        doc.fontSize(11).font('Helvetica').text(formattedPassage, {
            align: 'justify',
            indent: 20,
            lineGap: 2
        });
        doc.moveDown(1);

        // Question text
        doc.fontSize(12).font('Helvetica-Bold').text('Which choice completes the text so that it conforms to the conventions of Standard English?');
        doc.moveDown(0.5);

        // Answer choices
        (question.choices ?? question.answerChoices).forEach((choice, choiceIndex) => {
            const letter = String.fromCharCode(65 + choiceIndex); // A, B, C, D
            doc.fontSize(11).font('Helvetica').text(`${letter}. ${choice}`, {
                indent: 20,
                lineGap: 1
            });
        });

        doc.moveDown(2);

        // Correct answer
        const correctLetter = String.fromCharCode(65 + question.correctAnswer);
        doc.fontSize(12).font('Helvetica-Bold').text(`Correct Answer: ${correctLetter}`, {
            fillColor: 'green'
        });
        doc.fillColor('black'); // Reset color
        doc.moveDown(1);

        // Explanation
        doc.fontSize(12).font('Helvetica-Bold').text('Explanation:');
        doc.moveDown(0.3);
        doc.fontSize(11).font('Helvetica').text(question.explanation, {
            align: 'justify',
            lineGap: 2
        });

        // Add some space at the bottom
        doc.moveDown(2);
    });

    // Finalize the PDF
    doc.end();
    console.log(`PDF generated successfully: ${outputPath}`);
}