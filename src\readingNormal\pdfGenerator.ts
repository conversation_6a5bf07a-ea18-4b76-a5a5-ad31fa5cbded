import PDFDocument from 'pdfkit';
import fs from 'fs';
import { ReadingQuestionData } from '../types.ts';

export function generateReadingQuestionsPDF(questions: ReadingQuestionData[], outputPath: string = 'reading_questions.pdf'): void {
    const doc = new PDFDocument({
        margin: 50,
        size: 'A4'
    });

    doc.pipe(fs.createWriteStream(outputPath));

    doc.fontSize(20).font('Helvetica-Bold').text('SAT Reading Practice Questions', { align: 'center' });
    doc.moveDown(2);

    questions.forEach((question, index) => {
        if (index > 0) {
            doc.addPage();
        }

        doc.fontSize(16).font('Helvetica-Bold').text(`Question ${index + 1}`, { underline: true, continued: true });
        doc.fontSize(12).font('Helvetica').fillColor('blue').text(` (${question.difficulty} - ${question.questionType} - ${question.topic})`, { underline: false });
        doc.fillColor('black');
        doc.moveDown(1);

        if (question.intro) {
            doc.fontSize(11).font('Helvetica-Oblique').text(question.intro, {
                align: 'justify',
                lineGap: 2
            });
            doc.moveDown(1);
        }

        doc.fontSize(11).font('Helvetica').text(question.passage, {
            align: 'justify',
            indent: 20,
            lineGap: 2
        });

        if (question.passage2) {
            doc.moveDown(1);
            doc.fontSize(12).font('Helvetica-Bold').text('Passage 2:');
            doc.moveDown(0.5);
            doc.fontSize(11).font('Helvetica').text(question.passage2, {
                align: 'justify',
                indent: 20,
                lineGap: 2
            });
        }

        doc.moveDown(1.5);

        doc.fontSize(12).font('Helvetica-Bold').text(question.question);
        doc.moveDown(0.5);

        question.answerChoices.forEach((choice, choiceIndex) => {
            const letter = String.fromCharCode(65 + choiceIndex);
            doc.fontSize(11).font('Helvetica').text(`${letter}. ${choice}`, {
                indent: 20,
                lineGap: 1
            });
        });

        doc.moveDown(2);

        const correctLetter = String.fromCharCode(65 + question.correctAnswer);
        doc.fontSize(12).font('Helvetica-Bold').text(`Correct Answer: ${correctLetter}`, {
            fillColor: 'green'
        });
        doc.fillColor('black');
        doc.moveDown(1);

        doc.fontSize(12).font('Helvetica-Bold').text('Explanation:');
        doc.moveDown(0.3);
        doc.fontSize(11).font('Helvetica').text(question.explanation, {
            align: 'justify',
            lineGap: 2
        });

        doc.moveDown(2);
    });

    doc.end();
    console.log(`PDF generated successfully: ${outputPath}`);
}

export function generateReadingAnswerKey(questions: ReadingQuestionData[], outputPath: string = 'reading_answer_key.pdf'): void {
    const doc = new PDFDocument({
        margin: 50,
        size: 'A4'
    });

    doc.pipe(fs.createWriteStream(outputPath));

    doc.fontSize(18).font('Helvetica-Bold').text('Reading Questions Answer Key', { align: 'center' });
    doc.moveDown(2);

    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('Question #', 50, doc.y, { continued: true, width: 80 });
    doc.text('Type', 130, doc.y, { continued: true, width: 120 });
    doc.text('Difficulty', 250, doc.y, { continued: true, width: 80 });
    doc.text('Topic', 330, doc.y, { continued: true, width: 100 });
    doc.text('Answer', 430, doc.y, { width: 60 });
    doc.moveDown(0.5);

    doc.moveTo(50, doc.y).lineTo(490, doc.y).stroke();
    doc.moveDown(0.5);

    doc.font('Helvetica');
    questions.forEach((question, index) => {
        const correctLetter = String.fromCharCode(65 + question.correctAnswer);
        doc.text(`${index + 1}`, 50, doc.y, { continued: true, width: 80 });
        doc.text(question.questionType, 130, doc.y, { continued: true, width: 120 });
        doc.text(question.difficulty, 250, doc.y, { continued: true, width: 80 });
        doc.text(question.topic, 330, doc.y, { continued: true, width: 100 });
        doc.text(correctLetter, 430, doc.y, { width: 60 });
        doc.moveDown(0.3);
    });

    doc.end();
    console.log(`Answer key generated successfully: ${outputPath}`);
}