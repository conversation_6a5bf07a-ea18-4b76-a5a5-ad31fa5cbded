import fs from 'fs';
import { genAI } from '../ai';
import { Content, GenerateContentResponse, Type } from '@google/genai';
import { FullData, Result } from './types';
import { generateMathExplanation } from './mathAgent';

export const promptAddress = {
    "Word in Context": "./static/explanationPrompts/wordInContext.txt", // R1
    "Main Purpose Underlined": "./static/explanationPrompts/r2.txt",
    "Main Idea": "./static/explanationPrompts/r2.txt",
    "Main Purpose": "./static/explanationPrompts/r2.txt",
    "Overall Structure": "./static/explanationPrompts/r2.txt",
    "Specific Detail": "./static/explanationPrompts/r2.txt",
    "Inference": "./static/explanationPrompts/r3.txt",
    "Command of Evidence": "./static/explanationPrompts/r3.txt",
    "Paired Passage": "./static/explanationPrompts/pairedPassage.txt",
    "Graphs": "./static/explanationPrompts/graphs.txt",
    "Student Notes": "./static/explanationPrompts/studentNotes.txt",
    "Transitions": "./static/explanationPrompts/transitions.txt",
    "Punctuation": "./static/explanationPrompts/punctuation.txt",
    "Grammar": "./static/explanationPrompts/grammar.txt"
}

export async function genExplanation() {
    // get file address from command line arguments
    const filePath = process.argv[2];
    if (!filePath) {
        console.error('Please provide a file path as an argument');
        process.exit(1);
    }

    const fileName = filePath.split('\\').pop();

    const data: FullData[] = JSON.parse(fs.readFileSync(filePath, 'utf8'));

    const dataWithPrompt = data.map((questionData, index) => {
        try {
            if (index < 54) {
                return {
                    ...questionData,
                    prompt: fs.readFileSync(promptAddress[questionData.questionType], 'utf8')
                }
            } else {
                return questionData
            }
        } catch {
            throw new Error(`Prompt not found for question type: ${questionData.questionType}`);
        }
    })

    const promises = dataWithPrompt.map(async (questionData, index) => {
        // Get image path
        const imageFolder = fileName.replace('.json', '');

        let module: string;

        if (index < 27) {
            module = 'R1';
        } else if (index < 54) {
            module = 'R2';
        } else if (index < 76) {
            module = 'M1';
        } else {
            module = 'M2';
        }

        const imageFileName = `${module.replace('R', 'V')}_${questionData.question_number}`;
        const imagePath = `./dsat16-simulation-image/${imageFolder}/${imageFileName}.png`;
        
        // Verbal questions
        if (index < 54) {
            const contents: Content[] = [
                {
                    role: "user",
                    parts: [
                        {
                            text: `
                                ${questionData.intro ?? ""}\n
                                ${questionData.passage2 ? "Passage 1" : ""}\n
                                ${questionData.passage ?? ""}\n
                                ${questionData.passage2 ? "Passage 2" : ""}\n
                                ${questionData.passage2 ?? ""}\n
                                ${questionData.question ?? ""}\n
                                ${questionData.answer_choices.map((choice, index) => `${String.fromCharCode(65 + index)}. ${choice}`).join('\n')}\n
                                Return the index of the correct answer. For example, if the correct answer is A, return 0. Also return the explanation. Make sure that the explanation is split into small paragraphs for readability. In the explanation, refers to the answer choices as (A), (B), (C), or (D). Use <i> and <b> tags to format the answer.
                            `
                        }
                    ]
                }
            ]

            if (!questionData.is_text_only) contents[0].parts.push({
                inlineData: {
                    mimeType: 'image/png',
                    data: fs.readFileSync(imagePath, { encoding: "base64" }),
                }
            });

            const isWriting = ["Transitions", "Punctuation", "Grammar"].includes(questionData.questionType);;

            return genAI.models.generateContent({
                model: "gemini-2.5-pro",
                config: {
                    systemInstruction: [
                        {
                            // @ts-expect-error
                            text: questionData.prompt,
                        }
                    ],
                    thinkingConfig: {
                        thinkingBudget: isWriting ? 1024 : 128,
                    },
                    responseMimeType: 'application/json',
                    responseSchema: {
                    type: Type.OBJECT,
                    required: ["correctAnswer", "explanation"],
                    properties: {
                        correctAnswer: {
                        type: Type.INTEGER,
                        },
                        explanation: {
                        type: Type.STRING,
                        },
                    },
                    },
                },
                contents
            })
        } else { // Math questions
            return generateMathExplanation(questionData, questionData.is_text_only ? undefined : imagePath);
        }
    })

    const results = await Promise.all(promises);
    const explanationsData = results.map(result => {
        if (result instanceof GenerateContentResponse) {
            return JSON.parse(result.text!);
            // @ts-expect-error
        } else if (result?.finalOutput) return result.finalOutput as Result;
    });

    const finalData = data.map((d, i) => {
        return ({
            ...d,
            ...explanationsData[i]
        })
    });

    fs.writeFileSync(filePath, JSON.stringify(finalData, null, 2));
}