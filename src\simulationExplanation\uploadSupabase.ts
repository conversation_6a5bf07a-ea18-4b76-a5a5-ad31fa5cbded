import { supabase } from "../supabase";
import fs from 'fs';
import { FinalData } from "./types";

async function main() {
    // get file address from command line arguments
    const filePath = process.argv[2];

    if (!filePath) {
        console.error('Please provide a file path as an argument');
        process.exit(1);
    }

    const input = fs.readFileSync(filePath, 'utf8');

    const questions: FinalData[] = JSON.parse(input);

    const structuredQuestions = questions.map( (question, index) => {
        return {
            topic: question.topic,
            difficulty: question.difficulty,
            questionType: question.questionType,
            model: index < 54 ? "gemini-2.5-pro" : "gpt-5",
            intro: question.intro,
            passage: question.passage,
            passage2: question.passage2,
            question: question.question,
            answerChoices: question.answer_choices,
            correctAnswer: question.correctAnswer,
            explanation: question.explanation,
            createdAt: new Date().toISOString(),
            isSimulationQuestion: true
        }
    })

    const { data, error: questionError } = await supabase
        .from('question')
        .insert(structuredQuestions)
        .select('id');

    if (questionError) {
        console.error(questionError);
        return;
    }

    const { error: simulationError } = await supabase
        .from('simulations')
        .insert([{
            title: "2023 June V1",
            RW1: data.slice(0, 27).map(q => q.id),
            RW2: data.slice(27, 54).map(q => q.id),
            M1: data.slice(54, 76).map(q => q.id),
            M2: data.slice(76, 98).map(q => q.id)
        }])

    if (simulationError) {
        console.error(simulationError);
        // write data to file 
        fs.writeFileSync('./static/supabaseData.json', JSON.stringify(data, null, 2));
        return;
    }
}

main()