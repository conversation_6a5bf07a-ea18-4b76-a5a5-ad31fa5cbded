export type SimulationData = {
    question_number: number;
    intro?: string;
    passage?: string;
    passage2?: string;
    question: string;
    answer_choices?: string[];
    is_text_only: boolean;
    module?: string;
    id?: string;
}

export type ExtraData = {
    topic: string;
    difficulty: string;
    questionType: string;
}

export type FullData = SimulationData & ExtraData;

export type FinalData = FullData & Result;

export type Result = {
    correctAnswer: string;
    explanation: string;
}