<script lang="ts">
	import type { Snippet } from 'svelte';
	import type { BarChartData, GroupBarChartData, LineChartData, TableData } from './chart.types';

	type DataPoint = GroupBarChartData | BarChartData | TableData;
    

    interface Props {
        ticks: number[];
        isLegendsLayoutHorizontal?: boolean;
        isLayoutHorizontal: boolean;
        heading: string;
        sideLabel: string;
        bottomHeading?: string;
        hasXLabel?: boolean;
        paperName: string;
        author: string;
        chart: Snippet;
        legendsSnippet: Snippet;
    }

    let {
        isLegendsLayoutHorizontal = false,
        isLayoutHorizontal,
        heading,
        sideLabel,
        bottomHeading,
        paperName,
        author,
        chart,
        legendsSnippet
    }: Props = $props();

    let emptySpace = $state<HTMLDivElement | null>(null);
    let emptySpace2 = $state<HTMLDivElement | null>(null);
    let topHeading = $state<HTMLHeadingElement | null>(null);
    let legendsComponent = $state<HTMLDivElement | null>(null);
    let bottomHeadingComponent = $state<HTMLHeadingElement | null>(null);

    function handleResize() {
        if (topHeading?.clientHeight) {
            emptySpace!.style.height = `${topHeading!.clientHeight - 32}px`;
        }

        let newBottomHeight = 0;

        if (bottomHeadingComponent) {
            newBottomHeight += bottomHeadingComponent.clientHeight;
        }

        if (!isLayoutHorizontal && legendsComponent) newBottomHeight += legendsComponent.clientHeight;

        emptySpace2!.style.height = `${newBottomHeight}px`;
    }

    $effect(handleResize);
</script>

<svelte:window onresize={handleResize} />

<div class="flex flex-col gap-4 w-screen h-screen items-center justify-center">
    <div class="w-fit h-[700px] p-4 flex flex-col gap-4">
        <div class="w-fit h-full flex gap-20">
            <div class="flex flex-col">
                <div bind:this={emptySpace}></div>
                <h2 class="vertical-text text-center chart-text flex-1">{sideLabel}</h2>
                <div bind:this={emptySpace2}></div>
            </div>
            <div class="flex {isLayoutHorizontal ? 'flex-row gap-12' : `flex-col ${ bottomHeading ? "gap-4" : "gap-8" }`}">
                <div class="flex flex-col h-full max-w-[600px] gap-4">
                    <h2 bind:this={topHeading} class="text-center chart-text">{heading}</h2>
                    {@render chart()}
                    {#if bottomHeading}
                    <h2 bind:this={bottomHeadingComponent} class="text-center chart-text">{bottomHeading}</h2>
                    {/if}                    
                </div>

                <div bind:this={legendsComponent} class="legends border-2 border-black border-solid px-2 py-1 mt-2 flex {isLegendsLayoutHorizontal ? 'flex-row gap-8' : 'flex-col gap-4'}  w-fit self-center">
                    {@render legendsSnippet()}
                </div>
            </div>
        </div>
        <p class="text-[16px] font-['Inter']">Adapted from "{paperName}" by {author}</p>
    </div>
</div>

<style>
    .vertical-text {
        writing-mode: vertical-rl;
        transform: rotate(180deg);
    }
</style>