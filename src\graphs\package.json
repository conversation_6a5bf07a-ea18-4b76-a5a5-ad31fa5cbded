{"name": "graphs", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "devDependencies": {"@eslint/compat": "^1.4.0", "@eslint/js": "^9.36.0", "@sveltejs/adapter-auto": "^6.1.0", "@sveltejs/kit": "^2.43.2", "@sveltejs/vite-plugin-svelte": "^6.2.0", "@tailwindcss/vite": "^4.1.13", "@types/node": "^20", "eslint": "^9.36.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-svelte": "^3.12.4", "globals": "^16.4.0", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "svelte": "^5.39.5", "svelte-check": "^4.3.2", "tailwindcss": "^4.1.13", "typescript": "^5.9.2", "typescript-eslint": "^8.44.1", "vite": "^7.1.7", "vite-plugin-devtools-json": "^1.0.0"}, "dependencies": {"d3-array": "^3.2.4", "d3-scale": "^4.0.2", "layerchart": "^2.0.0-next.42"}}