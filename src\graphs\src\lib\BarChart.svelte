<script lang="ts">
	import { Bar<PERSON><PERSON> } from 'layerchart';
	import ChartWrapper from './ChartWrapper.svelte';
	import type { BarChartData } from './chart.types';

    interface Props {
        ticks: number[];
        data: BarChartData[];
        legend: string[];
        isLegendsLayoutHorizontal?: boolean;
        heading: string;
        sideLabel: string;
        bottomHeading?: string;
        hasXLabel?: boolean;
        paperName: string;
        author: string;
    }

    let props: Props = $props();

    let {
        ticks,
        data,
        legend,
        hasXLabel = false,
    } = $derived(props);

    // Switch to horizontal layout if any label is longer than 20 characters
    let isLayoutHorizontal = $derived(
        data.some(d => d.xLabel.length > 20)
    );
</script>

<ChartWrapper {...props} {isLayoutHorizontal}>
    {#snippet chart()}
        <BarChart
            {data}
            x="xLabel"
            y="value"
            yDomain={[Math.min(...ticks), Math.max(...ticks)]}
            props={{
                xAxis: {
                    format: "none",
                    tickLabelProps: { class: "chart-text translate-y-[12px]" }
                },
                yAxis: {
                    format: "metric",
                    ticks: ticks,
                    tickLabelProps: { class: "chart-number translate-y-[4px]" }
                },
                tooltip: {
                    header: { format: "none" },
                },
                bars: {
                    rounded: "none",
                    class: "stroke-[2px] fill-(--sky-blue)"
                }
            }}
            renderContext="svg"
            tooltip={false}
            grid={{
                y: {
                    class: "stroke-black stroke-[2px]"
                },
                yTicks: ticks,
            }}
            axis={hasXLabel || "y"}
        />
    {/snippet}

    {#snippet legendsSnippet()}
        <div class="flex gap-2 items-center">
            <div class="w-4 h-4 border bg-(--sky-blue)"></div>
            <span class="chart-text">{legend}</span>
        </div>
    {/snippet}
</ChartWrapper>