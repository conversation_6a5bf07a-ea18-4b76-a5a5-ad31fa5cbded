import { mailerlite } from "./mailerlite";

const SATDates = ["08-11-2025", "06-12-2025", "14-03-2026", "02-05-2026", "06-06-2026", "<PERSON><PERSON><PERSON><PERSON><PERSON>"];

interface Person {
    name: string;
    current_score: number;
    aim_score: number;
    test_date: string;
    dob: string;
}

async function main() {
    const response = await mailerlite.groups.getSubscribers("165159346133533984",{
        filter: {
            status: "active",
        },
        limit: 1000
    })

    const data = response.data.data;

    const filteredData = data.filter(person => {
        const fields = person.fields;
        // @ts-ignore
        const { current_score, name } = fields;
        return !!current_score && name !== "Le Hong Phuc";
    })

    const neededData: Person[] = filteredData.map(person => {
        const fields = person.fields;
        // @ts-ignore
        const { name, current_score, aim_score, test_date, dob } = fields;
        return {
            name,
            current_score,
            aim_score,
            test_date,
            dob
        }
    })

    console.table(neededData);

    // Count the number of people who is the target of the campaign
    const count = neededData.filter(person => {
        const { current_score, aim_score, test_date, dob } = person;

        // Born after 2008
        const wasBornAfter2008 = new Date(dob).getFullYear() > 2008;

        // Aim score is 300 more than current score
        const isAimScoreHighEnough = aim_score - current_score >= 300;

        // Test date is null or after April 2026
        const parsedTestDate = Date.parse(test_date);
        const testDate = (parsedTestDate ? new Date(parsedTestDate) : new Date(reformatDate(test_date)));
        const isTestDateNullOrFarAway = !test_date || test_date === "Chưa xác định" || testDate > new Date("01-04-2026");

        return wasBornAfter2008 || isAimScoreHighEnough || isTestDateNullOrFarAway;
    }).length;

    console.log(count); 
}

// Helper function to reformat date from "dd-mm-yyyy" to "yyyy-mm-dd"
function reformatDate(date: string) {
    if (date === "Chưa xác định" || !date) return date;

    const [day, month, year] = date.split('-');
    return `${year}-${month}-${day}`;
}

main()