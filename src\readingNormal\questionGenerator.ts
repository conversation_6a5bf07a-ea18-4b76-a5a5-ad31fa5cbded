import { GenerateContentConfig, GoogleGenAI, Type } from "@google/genai";
import fs from "node:fs";
import dotenv from 'dotenv';
import {
    WordInContextQuestion,
    MainIdeaQuestion,
    SpecificDetailQuestion,
    MainPurposeQuestion,
    InferenceQuestion,
    CommandOfEvidenceQuestion,
    PairedPassageQuestion,
    MainPurposeUnderlinedQuestion,
    OverallStructureQuestion,
    Question,
    StudentsNotesQuestion,
} from './questionTypes.ts';
import { supabase } from "../supabase.ts";
import { generateQuestionsPDF } from "../writing/pdfGenerator.ts";
import { generateReadingQuestionsPDF } from "./pdfGenerator.ts";
import { DifficultyLevel, TopicEntry } from '../types.ts';
import topicsJSON from '../../static/topics.json';
import { promptAddress } from "../simulationExplanation/genExplanation.ts";

dotenv.config();

const apiKey = process.env.GEMINI_API_KEY;
const genAI = new GoogleGenAI({apiKey: apiKey});



// Question type factory
function createQuestion(type: string, topic: string, difficulty: DifficultyLevel, evidenceType: string | null = null): Question {
    switch (type) {
        case 'Word in Context':
            return new WordInContextQuestion(topic, difficulty);
        case 'Main Idea':
            return new MainIdeaQuestion(topic, difficulty);
        case 'Specific Detail':
            return new SpecificDetailQuestion(topic, difficulty);
        case 'Main Purpose':
            return new MainPurposeQuestion(topic, difficulty);
        case 'Main Purpose Underlined':
            return new MainPurposeUnderlinedQuestion(topic, difficulty);
        case 'Overall Structure':
            return new OverallStructureQuestion(topic, difficulty);
        case 'Inference':
            return new InferenceQuestion(topic, difficulty);
        case 'Command of Evidence':
            return new CommandOfEvidenceQuestion(topic, difficulty, evidenceType!);
        case 'Paired Passage':
            return new PairedPassageQuestion(topic, difficulty);
        case 'Student Notes':
            return new StudentsNotesQuestion(topic, difficulty);
        default:
            throw new Error(`Unknown question type: ${type}`);
    }
}

export async function run(questionType: string, modelType: string, evidenceType: string | null = null, numQuestions: number) {    
    // Precompute unique selections to avoid race conditions AND ensure uniqueness within this run
    const topics = ['Social Science', 'Natural Science', 'Humanities'];
    const difficulties: DifficultyLevel[] = ['Easy', 'Medium', 'Hard'];
    const rerollCombinations = [
        { topic: 'Humanities', difficulty: 'Hard' as DifficultyLevel, type: 'Main Idea' },
        { topic: 'Natural Science', difficulty: 'Easy' as DifficultyLevel, type: 'Main Idea' },
        { topic: 'Social Science', difficulty: 'Medium' as DifficultyLevel, type: 'Main Idea' },
        { topic: 'Social Science', difficulty: 'Easy' as DifficultyLevel, type: 'Main Idea' },
        { topic: 'Humanities', difficulty: 'Easy' as DifficultyLevel, type: 'Main Idea' },
        { topic: 'Humanities', difficulty: 'Medium' as DifficultyLevel, type: 'Main Idea' },
        { topic: 'Natural Science', difficulty: 'Medium' as DifficultyLevel, type: 'Main Idea' },
        { topic: 'Social Science', difficulty: 'Medium' as DifficultyLevel, type: 'Overall Structure' },
        { topic: 'Natural Science', difficulty: 'Medium' as DifficultyLevel, type: 'Main Purpose' },
        { topic: 'Natural Science', difficulty: 'Hard' as DifficultyLevel, type: 'Command of Evidence' },
        { topic: 'Humanities', difficulty: 'Easy' as DifficultyLevel, type: 'Inference' },
        { topic: 'Humanities', difficulty: 'Medium' as DifficultyLevel, type: 'Command of Evidence' },
    ];

    const alreadySelected = new Set<string>(); // track topicEntry.topic used in this run

    function getAvailableTopics(): string[] {
        return topics.filter(t => (topicsJSON[t] as TopicEntry[]).some(e => !alreadySelected.has(e.topic)));
    }
    function pickTopicEntry(topic: string): TopicEntry | null {
        const pool = (topicsJSON[topic] as TopicEntry[]);
        let candidates = pool.filter(e => !e.hasTopicBeenUsed && !alreadySelected.has(e.topic));
        if (candidates.length === 0) {
            candidates = pool.filter(e => !alreadySelected.has(e.topic));
        }
        if (candidates.length === 0) return null;
        const entry = candidates[Math.floor(Math.random() * candidates.length)];
        alreadySelected.add(entry.topic);
        return entry;
    }

    const selections: { topic: string; difficulty: DifficultyLevel; topicEntry: TopicEntry }[] = [];
    for (let i = 0; i < numQuestions; i++) {
        let chosenTopic: string | null = null;
        let attempts = 0;
        while (attempts < 200) {
            attempts++;
            const availableTopics = getAvailableTopics();
            if (availableTopics.length === 0) break;
            const topic = availableTopics[Math.floor(Math.random() * availableTopics.length)];
            const difficulty = difficulties[Math.floor(Math.random() * difficulties.length)];
            const isForbidden = rerollCombinations.some(c => c.topic === topic && c.difficulty === difficulty && c.type === questionType);
            if (isForbidden) continue;
            // tentatively pick an entry to ensure availability
            const entry = pickTopicEntry(topic);
            if (!entry) continue; // try again with another topic
            chosenTopic = topic;

            selections.push({ topic, difficulty, topicEntry: entry });
            break;
        }
        if (!chosenTopic) {
            // No more unique entries available; stop scheduling more tasks
            break;
        }
    }

    // Create an array of promises for parallel execution based on precomputed unique selections
    const questionPromises = selections.map(async ({ topic, difficulty, topicEntry }, i) => {

        // Create question instance
        const question = createQuestion(questionType, topic, difficulty, evidenceType);        

        // Create generation config based on modelType
        let modelName: string;

        // Randomize temperature between 1 and 2
        const randomTemperature = 1 + Math.random();

        const config: GenerateContentConfig = {
            temperature: randomTemperature,
            topP: 0.95,
            topK: 64,
            maxOutputTokens: 8192,
            responseModalities: [],
            responseMimeType: "text/plain",
        };

        switch (modelType) {
            case 'gemini-2.5-flash':
                config.thinkingConfig = {
                    thinkingBudget: 0,
                };
                modelName = 'gemini-2.5-flash';
                break;

            case 'gemini-2.5-flash (with thinking)':
                config.thinkingConfig = {
                    thinkingBudget: 512,
                };
                modelName = 'gemini-2.5-flash';
                break;

            case 'gemini-2.5-pro':
                config.thinkingConfig = {
                    thinkingBudget: 128,
                };
                modelName = 'gemini-2.5-pro';
                break;
            default:
                throw new Error(`Unknown model type: ${modelType}`);
        }

        // Get difficulty-specific prompt
        const prompt = question.generatePrompt(topicEntry.topic);

        const result = await genAI.models.generateContent({
            model: modelName,
            contents: [
                {
                    role: 'user',
                    parts: [{ text: prompt }]
                }
            ],
            config: {
                ...config,
                tools: [{googleSearch:{}}],
                systemInstruction: {
                    text: question.systemInstructions
                }
            }
        });

        let text: string = result.candidates?.[0]?.content?.parts?.filter((p: any) => p.text?.startsWith("<passage>")).map((p: any) => p.text)[0];

        if (!text) {
            text = result.text;
        }

        if (!text) {
            console.error(`Failed to parse generated question #${i} for ${questionType} - ${topic} - ${difficulty}`);
            return null;
        }

        // Parse the generated question
        const passageMatch = text.match(/<passage>[ |\n]*([\s\S]*?)[ |\n]*<\/passage>/);
        const passage2Match = text.match(/<passage_2>[ |\n]*([\s\S]*?)[ |\n]*<\/passage_2>/) ?? "";
        const questionMatch = text.match(/<question>[ |\n]*([\s\S]*?)[ |\n]*<\/question>/);
        const answerChoicesMatch = text.match(/<answer_choices>[ |\n]*((?:[A-D]\).*\n?){4})[ |\n]*<\/answer_choices>/);
        const correctAnswerMatch = text.match(/<correct_answer>[ |\n]*([A-D])\)?(?:\s*)[ |\n]*<\/correct_answer>/);

        if (passageMatch && questionMatch && answerChoicesMatch && correctAnswerMatch) {
            const answerChoices = answerChoicesMatch[1].match(/^[A-D]\)\s*(.*)/gm)?.map((choice: string) => choice.replace(/^[A-D]\)\s*/, '').trim()) || [];

            let passage = passageMatch[1].trim();

            const questionData = {
                model: modelName,
                topic: topic,
                questionType: questionType,
                difficulty: difficulty,
                intro: question.intro ?? "",
                passage: passage,
                passage2: questionType === 'Paired Passage' ? passage2Match[1].trim() : "",
                explanation: undefined,
                question: questionMatch[1].trim(),
                answerChoices: answerChoices,
                correctAnswer: ['A', 'B', 'C', 'D'].indexOf(correctAnswerMatch[1].trim()),
                createdAt: new Date().toISOString(),
            };

            // Validate answer choices
            const correctAnswerIndex = ['A', 'B', 'C', 'D'].indexOf(correctAnswerMatch[1].trim()[0]);
            if (!question.validateAnswerChoices(answerChoices, correctAnswerIndex)) {
                console.error(`Invalid answer choices or correct answer index for question #${i}`);
                return null;
            }

            // Validate passage
            if (!question.validatePassage(passage)) {
                console.error(`Invalid passage for question #${i}`);
                return null;
            }

            // For certain question types, passage must contains a blank (___)
            if (['Word in Context', 'Specific Detail'].includes(questionType) && !passage.includes('___')) {
                console.error(`Passage does not contain a blank for question #${i}`);
                return null;
            }

            // For Main Purpose Underlined, passage must contain an underlined part
            if (questionType === 'Main Purpose Underlined' && !passage.includes('<u>')) {
                console.error(`Passage does not contain an underlined part for question #${i}`);
                return null;
            }

            // For Paired Passage, passage2 must be non-empty
            if (questionType === 'Paired Passage' && !passage2Match) {
                console.error(`Passage 2 is empty for question #${i}`);
                return null;
            }

            // For student notes, a | is needed
            if (questionType === 'Student Notes' && !passage.includes('|')) {
                console.error(`Passage does not contain a | for question #${i}`);
                return null;
            }
            
            const explanationPromptAddress = promptAddress[questionType];
            const explanationPrompt = fs.readFileSync(explanationPromptAddress, 'utf8');

            const explanationResult = await genAI.models.generateContent({
                model: "gemini-2.5-pro",
                config: {
                    systemInstruction: [
                        {
                            text: explanationPrompt,
                        }
                    ],
                    thinkingConfig: {
                        thinkingBudget: 128,
                    },
                    responseMimeType: 'application/json',
                    responseSchema: {
                    type: Type.OBJECT,
                    required: ["explanation"],
                    properties: {
                        explanation: {
                            type: Type.STRING,
                        },
                    },
                    },
                },
                contents: `
                    ${questionData.intro ?? ""}\n
                    ${questionData.passage2 ? "Passage 1" : ""}\n
                    ${questionData.passage ?? ""}\n
                    ${questionData.passage2 ? "Passage 2" : ""}\n
                    ${questionData.passage2 ?? ""}\n
                    ${questionData.question ?? ""}\n
                    ${questionData.answerChoices.map((choice, index) => `${String.fromCharCode(65 + index)}. ${choice}`).join('\n')}\n
                    Correct answer: ${String.fromCharCode(65 + questionData.correctAnswer)}\n\n
                    Return the explanation. Make sure that the explanation is split into small paragraphs for readability. In the explanation, refers to the answer choices as (A), (B), (C), or (D). Use <i> and <b> tags to format the answer.
                `                
            }).catch(error => {
                console.error('Error generating explanation:', error);
                return null;
            });

            questionData.explanation = JSON.parse(explanationResult.text).explanation;

            // TODO: turn this on
            // Store in Supabase
            const { error } = await supabase
                .from('question')
                .insert(questionData);

            if (error?.details) {
                console.log(`Error storing question #${i}:`, error.details);
                return null;
            }

            // Mark topic as used
            topicsJSON[topic].find((t: TopicEntry) => t.topic === topicEntry.topic)!.hasTopicBeenUsed = true;

            return questionData;
        } else {
            console.error(`Failed to parse generated question #${i} for ${questionType} - ${topic} - ${difficulty}`);
            const bugReport = {
                timestamp: new Date().toISOString().replace(/[-:Z]/g, '_'),
                inputParams: {
                    questionType,
                    topic,
                    difficulty,
                    evidenceType,
                    questionNumber: i,
                    modelType
                },
                parsingResults: {
                    hasPassage: !!passageMatch,
                    hasPassage2: !!passage2Match,
                    hasQuestion: !!questionMatch,
                    hasAnswerChoices: !!answerChoicesMatch,
                    hasCorrectAnswer: !!correctAnswerMatch,
                },
                systemState: {
                    systemInstructions: question.systemInstructions,
                    prompt: prompt,
                },
                rawResponse: result,
            };
            
            fs.writeFileSync(
                `bug_reports/${questionType.toLowerCase().replace(/\s+/g, '_')}_${difficulty}_${topic}_${bugReport.timestamp}.json`, 
                JSON.stringify(bugReport, null, 2)
            );
            return null;
        }
    });

    // Wait for all questions to complete
    const results = await Promise.all(questionPromises);
    
    // Filter out any null results (failed questions)
    const successfulQuestions = results.filter(result => result !== null);
    
    console.log(`Completed generating ${successfulQuestions.length} out of ${numQuestions} questions`);

    // TODO: enable this
    fs.writeFileSync('./static/topics.json', JSON.stringify(topicsJSON, null, 2));
    return successfulQuestions;
}

const questionTypes = [
    // { type: 'Word in Context', model: "gemini-2.5-flash (with thinking)", evidenceType: null, numQuestions: 300 },
    // { type: 'Main Idea', model: "gemini-2.5-flash", evidenceType: null, numQuestions: 60 },
    // { type: 'Specific Detail', model: "gemini-2.5-flash", evidenceType: null, numQuestions: 200 },
    // { type: 'Main Purpose', model: "gemini-2.5-flash", evidenceType: null, numQuestions: 60 },
    // { type: 'Main Purpose Underlined', model: "gemini-2.5-flash", evidenceType: null, numQuestions: 60 },
    // { type: 'Overall Structure', model: "gemini-2.5-flash", evidenceType: null, numQuestions: 60 },
    // { type: 'Inference', model: "gemini-2.5-flash (with thinking)", evidenceType: null, numQuestions: 250 },
    // { type: 'Command of Evidence', model: "gemini-2.5-flash (with thinking)", evidenceType: "support", numQuestions: 75 },
    // { type: 'Command of Evidence', model: "gemini-2.5-flash (with thinking)", evidenceType: "undermine", numQuestions: 75 },
    // { type: 'Paired Passage', model: "gemini-2.5-flash (with thinking)", evidenceType: null, numQuestions: 200 },
    // { type: 'Student Notes', model: "gemini-2.5-flash (with thinking)", evidenceType: null, numQuestions: 150 },
];

// Run all question types in parallel
Promise.all(questionTypes.map(qt => {
    return run(qt.type, qt.model, qt.evidenceType, qt.numQuestions);
})).then(results => {
    console.log('All questions completed!');
    const totalSuccess = results.reduce((sum, arr) => sum + arr.length, 0);
    console.log(`Successfully generated ${totalSuccess} questions in total`);
}).catch(error => {
    console.error('Error generating questions:', error);
});