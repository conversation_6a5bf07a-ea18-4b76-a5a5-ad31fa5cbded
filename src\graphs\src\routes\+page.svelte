<script>
	import BarChart from "$lib/BarChart.svelte";
	import GroupBarChart from "$lib/GroupBarChart.svelte";
	import LineChart from "$lib/LineChart.svelte";

    const data = {
        "ticks": [0, 10, 20, 30, 40, 50, 60, 70, 80, 90],
        "data": [
            {
                "xLabel": "2009/2010",
                "value1": 66,
                "value2": 22,
                "value3": 14
            },
            {
                "xLabel": "2010/2011",
                "value1": 65,
                "value2": 15,
                "value3": 14
            },
            {
                "xLabel": "2011/2012",
                "value1": 81,
                "value2": 33,
                "value3": 12
            },
            {
                "xLabel": "2012/2013",
                "value1": 70,
                "value2": 31,
                "value3": 7
            },
            {
                "xLabel": "2013/2014",
                "value1": 64,
                "value2": 27,
                "value3": 13
            }
        ],
        "heading": "Percentage of Maize Exported by Three Countries for Marketing Years 2009/2010–2013/2014",
        "sideLabel": "Percent"
    }
</script>

<LineChart 
    {...data}
    legends={["Argentina", "Brazil", "United States"]}
    paperName="Forecasting Wind Power at Multiple Lead Times Using Deep Learning"
    author="Dung Sex et. al."
    hasXLabel={true}
/>

<GroupBarChart
    ticks={[0, 25, 50, 75, 100, 125, 150, 175, 200, 225]}
    data={[
        { xLabel: '2020', value1: 150, value2: 185, },
        { xLabel: '2023', value1: 160, value2: 195, },
        { xLabel: '2026', value1: 170, value2: 210, },
    ]}
    legends={["West", "Midwest"]}
    isLegendsLayoutHorizontal={true}
    heading="Amount of Additional Electricity Wind Turbines Could Generate When Winds Were Stronger Than Forecast"
    sideLabel="Electricity (in thousands of MWh)"
    hasXLabel={true}    
    paperName="Forecasting Wind Power at Multiple Lead Times Using Deep Learning"
    author="Dung Sex et. al."
/>

<BarChart
    ticks={[0, 25, 50, 75, 100, 125, 150, 175]}
    data={[
        { xLabel: '2020', value: 150, }, 
        { xLabel: '2023', value: 160, }, 
    ]}
    legend={["West"]}
    isLegendsLayoutHorizontal={true}
    heading="Amount of Additional Electricity Wind Turbines Could Generate When Winds Were Stronger Than Forecast"
    sideLabel="Electricity (in thousands of MWh)"
    paperName="Forecasting Wind Power at Multiple Lead Times Using Deep Learning"
    author="Dung Sex et. al."
/>