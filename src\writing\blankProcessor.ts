#!/usr/bin/env node

import { GenerateContentConfig, Type } from "@google/genai";
import { anthropic, genAI } from "../ai.ts";
import { ProcessResult } from "../types.ts";
import { WritingQuestionType } from "./workflow.ts";
import fs from "fs";

export { ProcessResult };

/**
 * Tokenizes a string into words and punctuation
 * @param text - The text to tokenize
 * @returns Array of tokens (words and punctuation)
 */
function tokenize(text: string): string[] {
	// More inclusive regex for international text
	return text.match(/[\p{L}\p{N}]+|[^\p{L}\p{N}\s]/gu) || [];
}

/**
 * Determines if a token is a word or punctuation
 * @param token - The token to classify
 * @returns 'word' or 'punctuation'
 */
function getTokenType(token: string): "word" | "punctuation" {
	return /^[\p{L}\p{N}"'"'"]+$/u.test(token) ? "word" : "punctuation";
}

/**
 * Processes a passage containing a blank section and a list of answer choices.
 *
 * @param {string} passage - The passage containing a <blank>...</blank> section to be processed
 * @param {string[]} choices - Array of answer choices
 * @returns {ProcessResult} Object containing modified passage with blanks and shuffled choices
 * @throws {Error} If no blank section is found or if choices have invalid format
 */
export async function processPassageAndChoices(
	passage: string,
	choices: string[],
	questionType: WritingQuestionType
): Promise<ProcessResult> {
	// Extract the blank content
	const blankMatch = passage.match(/<blank>(.*?)<\/blank>/s);
	if (!blankMatch) {
		return {
			error: "No blank section found in passage",
		};
	}

	const blankContent = blankMatch[1].trim();

	// Replace the blank with 6 underscores
	let modifiedPassage = passage.replace(/<blank>.*?<\/blank>/s, "______");

	// Replace all newlines with spaces
	modifiedPassage = modifiedPassage.replace(/\n/g, " ");

	const systemInstruction = fs.readFileSync(
		`./static/writing_prompts/${questionType.toLowerCase()}/process.txt`,
		"utf8"
	);

	// Modify answer choices
	const contents =
		`New Correct Answer: ${blankContent}\n\n` +
		"Old Answer Choices:\n" +
		choices.map((choice) => `	"${choice}"`).join("\n") +
		"\n\nReturn the modified answer choices as a JSON with the key 'answerChoices', which is an array of strings. Return nothing else.";

	const response = await anthropic.messages.create({
		model: "claude-sonnet-4-5",
		system: [
			{
				type: "text",
				text: systemInstruction,
				cache_control: { 
                    type: "ephemeral",
                    ttl: "5m"
                }
			},
		],
		messages: [	
			{
				role: "user",
				content: contents,
			},
			{
				role: "assistant",
				content: "{ \"answerChoices\": ["
			}
		],
		max_tokens: 128
	});

	let result = "";
	response.content.forEach(c => {
		if (c.type === "text") result += c.text;
	})

	let modifiedChoices: string[];
	try {
		modifiedChoices = JSON.parse("{ \"answerChoices\": [" + result).answerChoices;
	} catch (error) {
		console.error("Raw result:", result);
		return {
			error: `Failed to parse AI response: ${error instanceof Error ? error.message : String(error)}`
		};
	}

	// Validate that modified choices preserve the punctuation pattern if question type is punctuation
	if (questionType === "Punctuation") {
		for (let i = 0; i < choices.length; i++) {
			const originalPunct = tokenize(choices[i]).filter((t) => getTokenType(t) === "punctuation");
			const modifiedPunct = tokenize(modifiedChoices[i]).filter((t) => getTokenType(t) === "punctuation");

			// We only care that punctuation tokens (e.g., commas, periods, quotes) appear in the same order
			const samePunct =
				originalPunct.length === modifiedPunct.length &&
				originalPunct.every((t, idx) => t === modifiedPunct[idx]);

			if (!samePunct) {
				console.log(`Original: ${choices[i]}, Modified: ${modifiedChoices[i]}`);
				return {
					error: `Choice ${i}: Punctuation mismatch. Original: "${originalPunct.join(" ")}", Modified: "${modifiedPunct.join(" ")}"`,
				};
			}
		}
	}

	// Shuffle the choices
	const shuffledChoices = [...modifiedChoices]
		.map((value) => ({ value, sort: Math.random() }))
		.sort((a, b) => a.sort - b.sort)
		.map(({ value }) => value.trim());

	const correctAnswerIndex = shuffledChoices.indexOf(blankContent.trim());

	return {
		modifiedPassage,
		shuffledChoices,
		correctAnswerIndex,
	};
}
