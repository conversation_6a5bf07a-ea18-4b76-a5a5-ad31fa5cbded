# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
AIGen is a TypeScript-based question generation system for standardized reading comprehension tests. It uses Google's Gemini AI to generate various types of SAT-style reading questions with passages, multiple-choice answers, and explanations.

## Core Architecture

### Main Components
1. **Question Generator (`src/readingNormal`)**: Used for generating questions for most of the reading section of the SAT.
2. **Writing Question Generator (`src/writing`)**: Used for generating questions for the writing section of the SAT.
3. **Explanation Generator (`src/simulationExplanation`)**: Used for generating explanations for the already made full-length practice tests.
4. **Graphs Generator (`src/graphs`)**: Used for generating graph questions for the reading section of the SAT.
5. **Database Integration**:
   - **Firebase** (`src/firebase.ts`) - Firebase Admin SDK for authentication and Firestore
   - **Supabase** (`src/supabase.ts`) - Primary database for storing generated questions

### Question Types Supported
- Word in Context
- Main Idea  
- Specific Detail
- Main Purpose
- Main Purpose Underlined
- Overall Structure
- Inference
- Command of Evidence (support/undermine variants)
- Paired Passage

### AI Models Configuration
The system supports multiple Gemini models with configurable thinking budgets:
- `gemini-2.5-flash-preview-05-20` (with/without thinking)
- `gemini-2.5-pro-preview-03-25`
- `gemini-2.5-pro-exp-03-25`

### Data Flow
1. Question generation runs in parallel batches
2. Each question gets random topic (Social Science/Natural Science/Humanities) and difficulty
3. AI generates structured responses with passages, questions, choices, and explanations
4. Responses are parsed and validated before database storage
5. Failed parsing attempts create detailed bug reports in `bug_reports/`

### Environment Configuration
Required environment variables:
- `GEMINI_API_KEY` - Google Gemini AI API key
- `SUPABASE_URL` - Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY` - Supabase service role key
- `FB_PROJECT_ID`, `FB_CLIENT_EMAIL`, `FB_PRIVATE_KEY` - Firebase credentials

### File Structure
- `src/` - Main source code
- `test/` - Test files  
- `dist/` - Compiled JavaScript output
- `bug_reports/` - Generated error reports for debugging
- `static/` - Static assets

### Key Patterns
- Async/await with Promise.all for parallel processing
- Factory pattern for question type creation
- Structured AI prompt engineering with XML-like tags
- Comprehensive error handling with detailed bug reporting
- Database integration with both Firebase and Supabase