import { genAI } from '../ai.ts';
import { processPassageAndChoices } from './blankProcessor.ts';
import { isQuestionValid } from './validator.ts';
import { QuestionData, TopicEntry } from '../types.ts';
import fs from 'fs';
import { generateExplanation } from './explanation.ts';

export { QuestionData };

export type WritingQuestionType = 'Transitions' | 'Punctuation' | 'Grammar';

const difficultyMap = { 'E': 'Easy', 'M': 'Medium', 'H': 'Hard' };

/**
 * This function takes in a question data object and generates a new question based on it.
 * The generation is done through 3 steps:
 * 1. Generate a new passage based on the original passage.
 * 2. Process the new passage to obtain new passage, choices, and correct answer.
 * 3. Generate an explanation for the new question based on the old explanation, new passage, and choices.
 * 
 * @param data the data of the old question
 * @returns QuestionObject 
 */
export async function createQuestion(data: QuestionData, questionType: WritingQuestionType, topic: "Social Science" | "Natural Science" | "Humanities", topicEntry: TopicEntry) {
    const { originalPassage, originalChoices, originalCorrectAnswerContent } = data;

    /**
     * * Step 1: Generate new passage based on original passage.
     */

    // Format originalPassage based on originalCorrectAnswer
    const formattedPassage = originalPassage.replace(/__+/, '<blank>' + originalCorrectAnswerContent + '</blank>');

    const passageSystemInstruction = fs.readFileSync(`./static/writing_prompts/${questionType.toLowerCase()}/newPassage.txt`, 'utf8');
    const result = await genAI.models.generateContent({
        model: "gemini-2.5-pro",
        config: {
            temperature: 0.55,
            systemInstruction: passageSystemInstruction,
            thinkingConfig: {
                thinkingBudget: 128,
            },
            tools: [{googleSearch:{}}],
        },
        contents: [
            {
                role: "user",
                parts: [{ 
                    text: formattedPassage + 
                        "\n\nUse Grounding with Google Search." + 
                        `\nThe topic of the passage is ${topic}. Specifically, ${topicEntry.topic}.`
                }]
            }
        ],
    });

    const newPassage = result.text!;

    // * Step 2: Process new passage to obtain new passage, choices, and correct answer
    const processResult = await processPassageAndChoices(newPassage, originalChoices, questionType);

    if ('error' in processResult) {
        console.error(processResult.error);
        return null;
    }

    const { modifiedPassage, shuffledChoices, correctAnswerIndex } = processResult;

    // * Step 3: Generate explanation for the new question based on the old explanation, new passage, and choices
    const explanation = await generateExplanation(modifiedPassage, shuffledChoices, correctAnswerIndex, data.rationale, questionType);

    // Create the question object
    const questionObject = {
        passage: modifiedPassage.replace(/\*([^*]+)\*/g, '<i>$1</i>'),
        question: "Which choice completes the text so that it conforms to the conventions of Standard English?",
        answerChoices: shuffledChoices,
        correctAnswer: correctAnswerIndex,
        explanation: explanation.replace(/\*([^*]+)\*/g, '<i>$1</i>'),
        difficulty: difficultyMap[data.difficulty],
        topic,
        questionType,
        model: "gemini-2.5-pro (question) + claude-sonnet-4-5 (explanation)",
    };

    // Validate the question object before returning
    if (!isQuestionValid(questionObject)) return null;

    return questionObject;
}