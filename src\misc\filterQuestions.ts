import { supabase } from "../supabase.ts";
import { ReadingQuestionData } from '../types.ts';

async function analyzeQuestions() {
    const invalidQuestionIds: number[] = [];
    let page = 0;
    const pageSize = 1000;
    let hasMore = true;

    while (hasMore) {
        const { data: questions, error } = await supabase
            .from('question')
            .select("*")
            .in('topic', ['Social Science', 'Natural Science', 'Humanities', 'Fiction'])
            .not("isSimulationQuestion", "eq", true)
            .range(page * pageSize, (page + 1) * pageSize - 1);
        
        console.log(questions.length)

        if (error) {
            console.error('Error fetching questions:', error);
            return;
        }

        if (questions.length === 0) {
            hasMore = false;
            break;
        }

        page += 1;

        questions.forEach((question) => {
            const { id, answerChoices, correctAnswer, passage, passage2, questionType, explanation } = question as unknown as ReadingQuestionData;
            let isValid = true;

            function validatePassage(passage: string): boolean {
                // Passage must contain at least one word and less than 180 words;
                return passage.trim().length > 0 &&
                    passage.trim().split(/\s+/).length < 180;
            }

            // Validate passage
            if (!validatePassage(passage)) {
                isValid = false;
            }

            // For certain question types, passage must contains a blank (___)
            if (['Word in Context', 'Specific Detail'].includes(questionType) && !passage.includes('___')) {
                isValid = false;
            }

            // For Main Purpose Underlined, passage must contain an underlined part
            if (questionType === 'Main Purpose Underlined' && !passage.includes('<u>')) {
                isValid = false;
            }

            // For Paired Passage, passage2 must be non-empty
            if (questionType === 'Paired Passage' && !passage2) {
                isValid = false;
            }

            // For student notes, a | is needed
            if (questionType === "Student Notes" && !passage.includes('|')) {
                isValid = false;
            }

            // explanation must be between 0 and 300 words
            if (explanation.trim().split(/\s+/).length > 300 || !explanation) {
                isValid = false;
            }

            if (!isValid) {
                invalidQuestionIds.push(id);
            }

        })
    }

    // Delete from supabase
    const { error } = await supabase
        .from('question')
        .delete()
        .in('id', invalidQuestionIds);

    if (error) {
        console.error('Error deleting questions:', error);
        return;
    }

    console.log(`Deleted ${invalidQuestionIds.length} invalid questions`);
}

analyzeQuestions()
