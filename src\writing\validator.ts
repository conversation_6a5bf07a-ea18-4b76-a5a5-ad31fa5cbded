#!/usr/bin/env node

import { QuestionObject } from '../types.ts';
import fs from 'fs';

/**
 * Validates a question object according to the specified requirements.
 * Prints a concise, structured warning if invalid.
 * This centralizes logging so it scales as rules evolve.
 *
 * @param question - The question object to validate
 * @returns {boolean} True if the question is valid, false otherwise
 */
export function isQuestionValid(question: QuestionObject): boolean {
    const { valid, errors } = validateQuestionDetailed(question);
    if (!valid) {
        logInvalidQuestion(question, errors);
    }
    return valid;
}

// Internal, scalable validation that accumulates all errors
// without changing the external boolean API.
type ValidationResult = { valid: boolean; errors: string[] };

function validateQuestionDetailed(question: QuestionObject): ValidationResult {
    const errors: string[] = [];
    try {
        // Check if question object exists
        if (!question || typeof question !== 'object') {
            errors.push('question missing or not an object');
            return { valid: false, errors };
        }

        // Validate required properties exist (maintain current semantics)
        const requiredProperties = ['passage', 'answerChoices', 'correctAnswer', 'explanation', 'difficulty'];
        for (const prop of requiredProperties) {
            if (!(prop in (question as any))) {
                errors.push(`missing property: ${prop}`);
            }
        }
        if (errors.length > 0) return { valid: false, errors };

        // Validate difficulty property
        if (!['Easy', 'Medium', 'Hard'].includes((question as any).difficulty)) {
            errors.push('difficulty must be one of Easy, Medium, Hard');
        }

        // Check for duplicate choices when present
        const choices = (question as any).answerChoices;
        if (Array.isArray(choices)) {
            const choiceSet = new Set(choices.map((c: string) => (typeof c === 'string' ? c.trim() : c)));
            if (choiceSet.size !== choices.length) {
                errors.push('duplicate choices found');
            }
        }

        // Validate choices property
        if (!validateChoices(choices)) {
            errors.push('choices must be an array of 4 non-empty strings');
        }

        // Validate correctAnswer property
        if (!validateCorrectAnswer((question as any).correctAnswer)) {
            errors.push('correctAnswer must be an integer in range 0..3');
        }

        // Validate passage property
        if (!validatePassage((question as any).passage)) {
            errors.push('passage must be a non-empty string with 10..120 words');
        }

        // Validate explanation property
        if (!validateExplanation((question as any).explanation)) {
            errors.push('explanation must be a non-empty string with <= 500 words');
        }

        // Validate that correctAnswer index corresponds to a valid choice
        if (Array.isArray(choices) && typeof (question as any).correctAnswer === 'number') {
            if (!validateCorrectAnswerIndex(choices, (question as any).correctAnswer)) {
                errors.push('correctAnswer index out of range or invalid at index');
            }
        }

        return { valid: errors.length === 0, errors };
    } catch (e: any) {
        errors.push(`exception during validation: ${e?.message ?? String(e)}`);
        return { valid: false, errors };
    }
}

function logInvalidQuestion(question: QuestionObject, errors: string[]): void {
    try {
        const preview = typeof question?.passage === 'string'
            ? question.passage.slice(0, 120).replace(/\s+/g, ' ')
            : '';
        const meta = {
            scope: 'writing.validation',
            severity: 'warn',
            difficulty: (question as any)?.difficulty,
            choicesLength: Array.isArray((question as any)?.answerChoices)
                ? (question as any).answerChoices.length
                : undefined,
        };
        // Single, structured warning for easy filtering and future extension
        console.warn('[Validation] Invalid question', { ...meta, errors, passagePreview: preview });

        // Save the whole question to a file
        fs.writeFileSync(`./bug_reports/${question.questionType}_${question.difficulty}_${question.topic}_${Date.now()}.json`, JSON.stringify(question, null, 2));
    } catch {
        // Fallback to avoid throwing from logger
        console.warn('[Validation] Invalid question (logging failed)', errors);
    }
}

/**
 * Validates the choices array
 */
function validateChoices(choices: any): boolean {
    // Check if choices is an array
    if (!Array.isArray(choices)) {
        return false;
    }

    // Check if choices has exactly 4 elements
    if (choices.length !== 4) {
        return false;
    }

    // Check if each choice is a non-empty string
    for (let i = 0; i < choices.length; i++) {
        const choice = choices[i];

        if (typeof choice !== 'string') {
            return false;
        }

        if (choice.trim().length === 0) {
            return false;
        }
    }

    return true;
}

/**
 * Validates the correctAnswer property
 */
function validateCorrectAnswer(correctAnswer: any): boolean {
    // Check if correctAnswer is a number
    if (typeof correctAnswer !== 'number') {
        return false;
    }

    // Check if correctAnswer is an integer
    if (!Number.isInteger(correctAnswer)) {
        return false;
    }

    // Check if correctAnswer is in valid range (0-3)
    if (correctAnswer < 0 || correctAnswer > 3) {
        return false;
    }

    return true;
}

/**
 * Validates the passage property
 */
function validatePassage(passage: any): boolean {
    // Check if passage is a string
    if (typeof passage !== 'string') {
        return false;
    }

    // Check if passage is non-empty
    if (passage.trim().length === 0) {
        return false;
    }

    // Count words in passage (split by whitespace and filter out empty strings)
    const words = passage.trim().split(/\s+/).filter(word => word.length > 0);
    const wordCount = words.length;

    // Check word count is between 10 and 120 words
    if (wordCount < 10) {
        return false;
    }

    if (wordCount > 120) {
        return false;
    }

    return true;
}

/**
 * Validates the explanation property
 */
function validateExplanation(explanation: any): boolean {
    // Check if explanation is a string
    if (typeof explanation !== 'string') {
        return false;
    }

    // Check if explanation is non-empty
    if (explanation.trim().length === 0) {
        return false;
    }

    // Check if explanation is shorter than 500 words
    if (explanation.trim().split(/\s+/).length > 500) {
        return false;
    }

    return true;
}

/**
 * Validates that the correctAnswer index corresponds to a valid choice
 */
function validateCorrectAnswerIndex(choices: string[], correctAnswer: number): boolean {
    // This validation assumes choices and correctAnswer have already been validated individually
    if (correctAnswer >= choices.length) {
        return false;
    }

    // Additional check to ensure the choice at the correct answer index is valid
    const correctChoice = choices[correctAnswer];
    if (!correctChoice || correctChoice.trim().length === 0) {
        return false;
    }

    return true;
}
