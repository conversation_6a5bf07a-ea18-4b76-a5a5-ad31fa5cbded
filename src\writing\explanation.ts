import fs from "fs";
import { anthropic, genAI } from "../ai";
import { WritingQuestionType } from "./workflow";
import { MessageParam } from "@anthropic-ai/sdk/resources/messages";

export async function generateExplanation(passage: string, choices: string[], correctAnswerIndex: number, rationale: string, questionType: WritingQuestionType) {
    const explanationSystemInstruction = fs.readFileSync(`./static/explanationPrompts/${questionType.toLowerCase()}.txt`, 'utf8');
    const model = 'claude-sonnet-4-5';
    const contents: MessageParam[] = [
        {
            role: 'user',
            content: `
                    ${passage}\n
                    Which choice completes the text so that it conforms to the conventions of Standard English?\n
                    ${choices.map((choice, index) => `${String.fromCharCode(65 + index)}. ${choice}`).join('\n')}\n
                    Correct answer: ${String.fromCharCode(65 + correctAnswerIndex)}\n\n

                    Return the explanation. Make sure that the explanation is split into small paragraphs for readability. In the explanation, refers to the answer choices as (A), (B), (C), or (D). Use <i> and <b> tags to format the answer. Do not use any other tags. Keep the answer concise.
                `,
        },
    ];

    const response = await anthropic.messages.create({
        model,
        system: [
            {
                type: "text",
                text: explanationSystemInstruction,
                cache_control: { 
                    type: "ephemeral",
                    ttl: "5m"
                }
            }
        ],
        messages: contents,
        max_tokens: 350
    });

    let result = ""

    response.content.forEach(c => {
        if (c.type === "text") result += c.text + "\n";
    })

    return result;
}