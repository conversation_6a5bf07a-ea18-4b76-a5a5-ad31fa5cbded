import { mailerlite } from "./mailerlite";

async function main() {
    const response = await mailerlite.groups.getSubscribers("165159346133533984",{
        filter: {
            status: "active",
        },
        limit: 1000
    })

    const data = response.data.data;

    const promises = data.map(async person => {
        return mailerlite.groups.assignSubscriber(person.id, "165192362391242517");
    });

    await Promise.all(promises);
}

main()