<script lang="ts">
  import Side<PERSON>abel from './SideLabel.svelte';

	import { BarChart } from 'layerchart';

	type DataPoint = {
		xLabel: string; 
		value1: number;
        value2: number;
        value3?: number;
	};
    

    interface Props {
        ticks: number[];
        data: DataPoint[];
        legends: string[];
        isLegendsLayoutHorizontal?: boolean;
        heading: string;
        sideLabel: string;
        bottomHeading?: string;
        hasXLabel?: boolean;
        paperName: string;
        author: string;
    }

    let {
        ticks,
        data,
        legends,
        isLegendsLayoutHorizontal = false,
        heading,
        sideLabel,
        bottomHeading,
        hasXLabel = false,
        paperName,
        author
    }: Props = $props();


    const colors = ["--yellow", "--aquamarine", "--rose"];
    const keys: (keyof DataPoint)[] = ["value1", "value2", "value3"];

    // Switch to horizontal layout if any label is longer than 20 characters
    let isLayoutHorizontal = $derived(data.some(d => d.xLabel.length > 20));

    // Filter out undefined values
    const series = keys.filter(key => data[0][key]).map((key, index) => ({ key, color: `var(${colors[index]})` }));

    let emptySpace = $state<HTMLDivElement | null>(null);
    let emptySpace2 = $state<HTMLDivElement | null>(null);
    let topHeading = $state<HTMLHeadingElement | null>(null);
    let legendsComponent = $state<HTMLDivElement | null>(null);
    let bottomHeadingComponent = $state<HTMLHeadingElement | null>(null);

    function handleResize() {
        if (topHeading?.clientHeight) {
            emptySpace!.style.height = `${topHeading!.clientHeight - 32}px`;
        }

        let newBottomHeight = 0;

        if (bottomHeadingComponent) {
            newBottomHeight += bottomHeadingComponent.clientHeight;
        }

        if (!isLayoutHorizontal && legendsComponent) newBottomHeight += legendsComponent.clientHeight;

        emptySpace2!.style.height = `${newBottomHeight}px`;
    }

    $effect(handleResize);
</script>

                    <div class="flex gap-2 items-center">
                        <div class="w-4 h-4 border" style:background-color="var({color})"></div>
                        <span class="font-['Noto_Serif'] text-[22px]">{legend}</span>
                    </div>

<style>
    .vertical-text {
        writing-mode: vertical-rl;
        transform: rotate(180deg);
    }
</style>

