import fs from 'fs';
import { genAI } from '../ai';
import { Type } from '@google/genai';
import { ExtraData, SimulationData } from './types';

export async function enrichData() {
    // get file address from command line arguments
    const filePath = process.argv[2];
    if (!filePath) {
        console.error('Please provide a file path as an argument');
        process.exit(1);
    }

    const data: SimulationData[] = JSON.parse(fs.readFileSync(filePath, 'utf8'));

    const verbalSystemInstruction = fs.readFileSync('./static/explanationPrompts/enrichVerbal.txt', 'utf8');
    const mathSystemInstruction = fs.readFileSync('./static/explanationPrompts/enrichMath.txt', 'utf8');

    // Map first 54 questions to verbal and 44 to math
    const dataWithInstruction = data.map((data, index) => {
        return {
            ...data,
            systemInstruction: index < 54 ? verbalSystemInstruction : mathSystemInstruction
        }
    })

    const promises = dataWithInstruction.map(async (question) => {
        return genAI.models.generateContent({
            model: "gemini-2.5-flash",
            config: {
                systemInstruction: [
                    {
                        text: question.systemInstruction,
                    }
                ],
                thinkingConfig: {
                    thinkingBudget: 0,
                },
                responseMimeType: 'application/json',
                responseSchema: {
                type: Type.OBJECT,
                required: ["topic", "difficulty", "questionType"],
                properties: {
                    topic: {
                        type: Type.STRING,
                    },
                    difficulty: {
                        type: Type.STRING,
                    },
                    questionType: {
                        type: Type.STRING,
                    },
                    },
                },
            },
            contents: [
                {
                    role: "user",
                    parts: [
                        {
                            text: `${question.passage}\n${question.question}\n${question.answer_choices ? question.answer_choices.join('\n') : ""}`
                        }
                    ]
                }
            ],
        });
    })

    const results = await Promise.all(promises);

    const newData = results.map(r => JSON.parse(r.text) as ExtraData);

    // merge newData with data (only first 54)
    // @ts-expect-error
    const finalData: FullData[] = data.map((d, i) => {
        if (i >= newData.length) return d;
        return ({
            ...d,
            ...newData[i]
        })
    });

    finalData.forEach(d => {
        if (!d.is_text_only) {
            d.questionType = "Graphs";
        }
    })

    fs.writeFileSync(filePath, JSON.stringify(finalData, null, 2));
}