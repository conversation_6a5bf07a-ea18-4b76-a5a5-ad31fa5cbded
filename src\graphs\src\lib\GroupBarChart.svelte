<script lang="ts">
	import { BarChart } from 'layerchart';
	import ChartWrapper from './ChartWrapper.svelte';
	import type { GroupBarChartData } from './chart.types';

    interface Props {
        ticks: number[];
        data: GroupBarChartData[];
        legends: string[];
        isLegendsLayoutHorizontal?: boolean;
        heading: string;
        sideLabel: string;
        bottomHeading?: string;
        hasXLabel?: boolean;
        paperName: string;
        author: string;
    }

    let props: Props = $props();

    let {
        ticks,
        data,
        legends,
        hasXLabel = false,
    } = $derived(props);


    const colors = ["--yellow", "--aquamarine", "--rose"];
    const keys: (keyof GroupBarChartData)[] = ["value1", "value2", "value3"];

    // Filter out undefined values
    const series = keys.filter(key => data[0][key]).map((key, index) => ({ key, color: `var(${colors[index]})` }));
    
    // Switch to horizontal layout if any label is longer than 20 characters
    let isLayoutHorizontal = $derived(
        data.some(d => d.xLabel.length > 20)
    );
</script>

<ChartWrapper {...props} {isLayoutHorizontal}>
    {#snippet chart()}
    <BarChart
        {data}
        x="xLabel"
        {series}
        yDomain={[Math.min(...ticks), Math.max(...ticks)]}
        seriesLayout="group"
        props={{
            xAxis: {
                format: "none",
                tickLabelProps: { class: "chart-text translate-y-[12px]" }
            },
            yAxis: {
                format: "metric",
                ticks: ticks,
                tickLabelProps: { class: "chart-number translate-y-[4px]" }
            },
            tooltip: {
                header: { format: "none" },
            },
            bars: {
                rounded: "none",
                class: "stroke-[2px]"
            }
        }}
        renderContext="svg"
        tooltip={false}
        grid={{
            y: {
                class: "stroke-black stroke-[2px]"
            },
            yTicks: ticks,
        }}
        axis={hasXLabel || "y"}
    />
    {/snippet}

    {#snippet legendsSnippet()}
        {#each legends as legend, index}
        {@const color = colors[index]}
        <div class="flex gap-2 items-center">
            <div class="w-4 h-4 border" style:background-color="var({color})"></div>
            <span class="chart-text">{legend}</span>
        </div>
        {/each}
    {/snippet}
</ChartWrapper>