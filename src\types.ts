// Centralized type definitions for AIGen project

// =======================
// Core Types
// =======================

export type DifficultyLevel = 'Easy' | 'Medium' | 'Hard';

// =======================
// Question Data Types
// =======================

export interface Question {
    questionType: string;
    difficulty: string;
    topic: string;
    explanation: string;
}

export interface ReadingQuestionData {
    id: number;
    model: string;
    topic: string;
    questionType: string;
    difficulty: string;
    intro: string;
    passage: string;
    passage2?: string;
    question: string;
    answerChoices: string[];
    correctAnswer: number;
    explanation: string;
    createdAt: string;
}

export interface QuestionResult {
    passage: string;
    choices?: string[];
    answerChoices?: string[];
    correctAnswer: number;
    explanation: string;
    difficulty: string;
}

export interface QuestionData {
    originalPassage: string;
    originalChoices: string[];
    originalCorrectAnswerContent: string;
    difficulty: string;
    rationale: string;
}

export interface QuestionObject {
    passage: string;
    answerChoices: string[];
    correctAnswer: number;
    explanation: string;
    difficulty: string;
    questionType: string;
    topic: string;
    model: string;
    question: string;
}

// =======================
// Processing Result Types
// =======================

export type ProcessResult = {
    modifiedPassage: string;
    shuffledChoices: string[];
    correctAnswerIndex: number;
} | {
    error: string;
}

export interface TopicEntry {
  topic: string;
  hasTopicBeenUsed: boolean;
}

export interface TopicsData {
  [category: string]: TopicEntry[];
}