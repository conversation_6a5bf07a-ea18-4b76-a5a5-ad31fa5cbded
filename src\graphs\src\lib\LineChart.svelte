<script lang="ts">
	import { Line<PERSON>hart } from 'layerchart';
	import ChartWrapper from './ChartWrapper.svelte';
	import type { LineChartData } from './chart.types';

    interface Props {
        ticks: number[];
        data: LineChartData[];
        legends: string[];
        isLegendsLayoutHorizontal?: boolean;
        heading: string;
        sideLabel: string;
        bottomHeading?: string;
        hasXLabel?: boolean;
        paperName: string;
        author: string;
    }

    let props: Props = $props();

    let {
        ticks,
        data,
        legends,
        hasXLabel = false,
    } = $derived(props);

    // Switch to horizontal layout if any label is longer than 20 characters
    let isLayoutHorizontal = $derived(
        data.some(d => d.xLabel.length > 20)
    );

    // Get Series
    const colors = ["--sky-blue", "--yellow", "--aquamarine", "--rose", "--purple", "--tangerine"];
    let keys: string[] = $derived(Object.keys(data[0]).filter(key => key !== 'xLabel'));
    let series = $derived(keys.map( (key, index) => ({ key, color: `var(${colors[index]})` })));
</script>

<ChartWrapper {...props} {isLayoutHorizontal}>
    {#snippet chart()}
        <LineChart
            data={data}
            x="xLabel"
            yDomain={[Math.min(...ticks), Math.max(...ticks)]}
            {series}
            props={{
                xAxis: {
                    format: "none",
                    tickLabelProps: { class: "chart-text translate-y-[12px]" },
                    labelProps: { style: "transform: rotate(45deg); transform-origin: left bottom;" }
                },
                yAxis: {
                    format: "metric",
                    ticks: ticks,
                    tickLabelProps: { class: "chart-number translate-y-[4px]" }
                },
                tooltip: {
                    header: { format: "none" },
                },
                spline: {
                    strokeWidth: 4
                }
            }}
            renderContext="svg"
            tooltip={false}
            grid={{
                y: {
                    class: "stroke-black stroke-[2px]"
                },
                yTicks: ticks,
                x: {
                    class: "stroke-black stroke-[2px]"
                },
                xTicks: data.map(d => d.xLabel)
            }}
            axis={hasXLabel || "y"}
        />
    {/snippet}

    {#snippet legendsSnippet()}
        {#each legends as legend, index}
            {@const color = colors[index]}
            <div class="flex gap-2 items-center">
                <div class="w-4 h-4 border" style:background-color="var({color})"></div>
                <span class="chart-text">{legend}</span>
            </div>
        {/each}
    {/snippet}
</ChartWrapper>